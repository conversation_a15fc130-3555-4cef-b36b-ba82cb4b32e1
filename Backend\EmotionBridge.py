import os
import json
import time
import threading
from typing import Dict, Optional

class EmotionBridge:
    """
    Bridge between emotion detection system and GUI display.
    Monitors emotion data and updates the GUI emotion file in real-time.
    """
    
    def __init__(self):
        self.data_dir = os.path.join(os.getcwd(), "Data")
        self.frontend_dir = os.path.join(os.getcwd(), "Frontend", "Files")
        
        # Source files (where emotion data is written)
        self.emotion_data_file = os.path.join(self.data_dir, "EmotionData.json")
        self.emotion_context_file = os.path.join(self.frontend_dir, "EmotionContext.json")
        
        # Target file (where G<PERSON> reads from)
        self.gui_emotion_file = os.path.join(self.frontend_dir, "Emotion.data")
        
        # Ensure directories exist
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.frontend_dir, exist_ok=True)
        
        # Bridge state
        self.is_running = False
        self.update_thread = None
        self.last_emotion = "Neutral"
        self.last_update_time = 0
        self.update_interval = 0.5  # Update every 500ms for real-time feel
        
        print("✅ Emotion Bridge initialized")
    
    def get_current_emotion_from_sources(self) -> str:
        """Get the most recent emotion from available sources."""
        emotions = []
        
        # Try to get emotion from EmotionData.json (from EmotionAnalysis.py)
        try:
            if os.path.exists(self.emotion_data_file):
                with open(self.emotion_data_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    emotion = data.get("current_emotion", "neutral")
                    timestamp = data.get("last_updated", 0)
                    emotions.append((emotion, timestamp, "analysis"))
        except Exception as e:
            pass
        
        # Try to get emotion from EmotionContext.json (from VoskSpeechToText.py)
        try:
            if os.path.exists(self.emotion_context_file):
                with open(self.emotion_context_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    emotion = data.get("emotion", "neutral")
                    timestamp = data.get("timestamp", 0)
                    emotions.append((emotion, timestamp, "context"))
        except Exception as e:
            pass
        
        # Return the most recent emotion
        if emotions:
            # Sort by timestamp and get the most recent
            emotions.sort(key=lambda x: x[1], reverse=True)
            most_recent = emotions[0]
            return most_recent[0].capitalize()  # Capitalize for display
        
        return "Neutral"
    
    def update_gui_emotion_file(self, emotion: str):
        """Update the GUI emotion file with the current emotion."""
        try:
            with open(self.gui_emotion_file, "w", encoding="utf-8") as f:
                f.write(emotion)
            return True
        except Exception as e:
            print(f"❌ Error updating GUI emotion file: {e}")
            return False
    
    def monitor_emotions(self):
        """Main monitoring loop that runs in a separate thread."""
        print("🔄 Starting emotion monitoring...")
        
        while self.is_running:
            try:
                # Get current emotion from sources
                current_emotion = self.get_current_emotion_from_sources()
                
                # Update GUI file if emotion changed or enough time has passed
                current_time = time.time()
                if (current_emotion != self.last_emotion or 
                    current_time - self.last_update_time > self.update_interval):
                    
                    if self.update_gui_emotion_file(current_emotion):
                        if current_emotion != self.last_emotion:
                            print(f"🎭 Emotion updated: {self.last_emotion} → {current_emotion}")
                        
                        self.last_emotion = current_emotion
                        self.last_update_time = current_time
                
                # Sleep for a short interval
                time.sleep(0.1)
                
            except Exception as e:
                print(f"❌ Error in emotion monitoring: {e}")
                time.sleep(1)  # Wait longer on error
    
    def start_monitoring(self):
        """Start the emotion monitoring in a background thread."""
        if self.is_running:
            print("⚠️ Emotion monitoring is already running")
            return
        
        self.is_running = True
        self.update_thread = threading.Thread(target=self.monitor_emotions, daemon=True)
        self.update_thread.start()
        print("✅ Emotion monitoring started")
    
    def stop_monitoring(self):
        """Stop the emotion monitoring."""
        if not self.is_running:
            print("⚠️ Emotion monitoring is not running")
            return
        
        self.is_running = False
        if self.update_thread:
            self.update_thread.join(timeout=2)
        print("🛑 Emotion monitoring stopped")
    
    def get_status(self) -> Dict[str, any]:
        """Get the current status of the emotion bridge."""
        return {
            "is_running": self.is_running,
            "last_emotion": self.last_emotion,
            "last_update_time": self.last_update_time,
            "update_interval": self.update_interval,
            "emotion_data_exists": os.path.exists(self.emotion_data_file),
            "emotion_context_exists": os.path.exists(self.emotion_context_file),
            "gui_emotion_file_exists": os.path.exists(self.gui_emotion_file)
        }
    
    def force_update(self):
        """Force an immediate emotion update."""
        current_emotion = self.get_current_emotion_from_sources()
        if self.update_gui_emotion_file(current_emotion):
            print(f"🔄 Forced emotion update: {current_emotion}")
            self.last_emotion = current_emotion
            self.last_update_time = time.time()
            return True
        return False

# Global emotion bridge instance
emotion_bridge = EmotionBridge()

def start_emotion_bridge():
    """Start the emotion bridge monitoring."""
    emotion_bridge.start_monitoring()

def stop_emotion_bridge():
    """Stop the emotion bridge monitoring."""
    emotion_bridge.stop_monitoring()

def get_emotion_bridge_status():
    """Get emotion bridge status."""
    return emotion_bridge.get_status()

def force_emotion_update():
    """Force an emotion update."""
    return emotion_bridge.force_update()

if __name__ == "__main__":
    # Test the emotion bridge
    print("Testing Emotion Bridge...")
    
    # Start monitoring
    start_emotion_bridge()
    
    try:
        # Run for a few seconds to test
        time.sleep(5)
        
        # Show status
        status = get_emotion_bridge_status()
        print(f"Bridge status: {status}")
        
        # Force an update
        force_emotion_update()
        
    except KeyboardInterrupt:
        print("\nStopping emotion bridge...")
    finally:
        stop_emotion_bridge()
